import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.ticker import ScalarFormatter
from matplotlib import dates as mdates
# ========== 中文字体配置 ==========
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows系统字体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示异常

def generate_trend_plot(
    csv_path: str, 
    output_folder: str, 
    time_range: str = None,
    figsize: tuple = (22, 30),
    dpi: int = 300
):
    """
    :param csv_path: CSV文件路径
    :param output_folder: 图片输出目录
    :param time_range: 时间范围字符串（用于标题）
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    """
    # ========== 参数校验 ==========
    csv_file = Path(csv_path)
    if not csv_file.exists():
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
    
    name_map = {
    # 原有特征
    'DEP': '井深',
    'BITDEP': '钻头位置',
    'HOKHEI': '大钩负荷',
    'DRITIME': '钻时',
    'WOB': '钻压',
    'HKLD': '大钩高度',
    'RPM': '转速',
    'TOR': '扭矩',
    
    # 新增特征
    'FLOWIN': '入口流量',
    'FLOWOUT': '出口流量',
    'SPP': '泵压',
    'CSIP': '套压'
}

    # ========== 配置输出路径 ==========
    output_dir = Path(output_folder)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成图片文件名（与CSV文件名保持一致）
    img_name = f"{csv_file.stem}.png"
    img_path = output_dir / img_name

    # ========== 数据准备 ==========
    try:
        df = pd.read_csv(csv_file)
        df['date'] = pd.to_datetime(df['date'])
    except Exception as e:
        raise ValueError(f"CSV文件读取失败: {str(e)}")

    # ========== 图表配置 ==========
    plt.style.use('seaborn-v0_8-colorblind')
    features = [
        'DEP', 'BITDEP', 'HOKHEI', 'DRITIME',
        'WOB', 'HKLD', 'RPM', 'TOR',
        'FLOWIN', 'FLOWOUT', 'SPP', 'CSIP'
    ]
    
    # ========== 创建图表 ==========
    fig, axes = plt.subplots(len(features), 1, figsize=(22, 1.2*len(features)), sharex=True)
    
    # 绘制每个特征
    colors = plt.get_cmap('tab10')
    for i, col in enumerate(features):
        axes[i].plot(df['date'], df[col], color=colors(i%10), label=name_map[col])
        axes[i].set_ylabel(name_map[col], rotation=0, labelpad=20, ha='right')
        axes[i].legend(loc='best', frameon=True)
        axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))
        axes[i].ticklabel_format(style='plain', axis='y')

    # ========== 时间轴格式设置 ==========
    for ax in axes:
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))

    # 调整底部标签角度
    axes[-1].tick_params(axis='x', rotation=0)

    # ========== 标题和布局 ==========
    title = f'{csv_file.stem} 参数趋势'
    if time_range:
        title += f' ({time_range})'
    fig.suptitle(title, y=0.98)
    plt.tight_layout(h_pad=1.5)

    # ========== 保存输出 ==========
    plt.savefig(img_path, bbox_inches='tight', dpi=dpi)
    plt.close()
    print(f"✅ 趋势图生成成功: {img_path}")
    return str(img_path)


def batch_process_csv_files(
    input_folder: str = ".",
    output_folder: str = "charts",
    figsize: tuple = (22, 25),
    dpi: int = 200
):
    """
    批量处理目录中的所有CSV文件，生成趋势图

    :param input_folder: CSV文件所在目录，默认为当前目录
    :param output_folder: 图片输出目录，默认为 "charts"
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    """
    # ========== 扫描CSV文件 ==========
    input_path = Path(input_folder)
    csv_files = list(input_path.glob("*.csv"))

    if not csv_files:
        print("❌ 未找到任何CSV文件")
        return

    print(f"📁 发现 {len(csv_files)} 个CSV文件")
    print(f"📊 输出目录: {output_folder}")
    print("-" * 50)

    # ========== 创建输出目录 ==========
    output_path = Path(output_folder)
    output_path.mkdir(parents=True, exist_ok=True)

    # ========== 批量处理 ==========
    success_count = 0
    failed_files = []

    for i, csv_file in enumerate(csv_files, 1):
        try:
            print(f"🔄 正在处理 {i}/{len(csv_files)}: {csv_file.name}")

            # 调用原有的处理函数
            generate_trend_plot(
                csv_path=str(csv_file),
                output_folder=output_folder,
                figsize=figsize,
                dpi=dpi
            )
            success_count += 1

        except Exception as e:
            error_msg = f"❌ 处理失败 {csv_file.name}: {str(e)}"
            print(error_msg)
            failed_files.append((csv_file.name, str(e)))
            continue

    # ========== 处理结果总结 ==========
    print("-" * 50)
    print(f"📈 处理完成！")
    print(f"✅ 成功处理: {success_count} 个文件")
    print(f"❌ 处理失败: {len(failed_files)} 个文件")

    if failed_files:
        print("\n失败文件详情:")
        for filename, error in failed_files:
            print(f"  • {filename}: {error}")

    print(f"\n📂 所有图表已保存到: {output_path.absolute()}")
    return success_count, failed_files

# ===== 使用示例 =====
if __name__ == "__main__":
    # 批量处理当前目录下的所有CSV文件
    batch_process_csv_files(
        input_folder=".",      # 当前目录
        output_folder="charts", # 输出到 charts 子文件夹
        figsize=(22, 25),
        dpi=200
    )

    # 如果需要处理单个文件，可以使用：
    # generate_trend_plot(
    #     csv_path="your_file.csv",
    #     output_folder="charts",
    #     figsize=(22, 25),
    #     dpi=200
    # )