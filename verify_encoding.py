import os

# 检查文件是否包含UTF-8 BOM标记
def check_bom(file_path):
    with open(file_path, 'rb') as f:
        first_bytes = f.read(3)
        if first_bytes == b'\xef\xbb\xbf':
            return "UTF-8 with BOM"
        else:
            return "UTF-8 without BOM or other encoding"

# 检查几个处理后的文件
test_files = [
    '已处理-工况/充浅1实时数据.csv',
    '已处理-工况/宜203H1-2实时数据.csv'
]

print("=== 编码验证结果 ===")
for file_path in test_files:
    if os.path.exists(file_path):
        encoding_info = check_bom(file_path)
        print(f"{file_path}: {encoding_info}")
        
        # 读取前几行验证内容
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()[:3]
            print(f"  前3行内容:")
            for i, line in enumerate(lines):
                print(f"    {i+1}: {line.strip()}")
        print()
    else:
        print(f"{file_path}: 文件不存在")

print("✅ 验证完成！如果显示'UTF-8 with BOM'，则Excel应该能正确识别中文字符。")
