import sys
import os
sys.path.append('已处理-工况')

from visual import generate_rigsta_only_plot, generate_trend_plot
import pandas as pd

# 测试工况可视化功能
def test_rigsta_visualization():
    print("=== 测试工况可视化功能 ===")
    
    # 选择一个测试文件
    test_file = "已处理-工况/充浅1实时数据.csv"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    # 读取数据查看工况分布
    df = pd.read_csv(test_file, encoding='utf-8-sig')
    print(f"文件: {test_file}")
    print(f"数据行数: {len(df)}")
    print(f"时间范围: {df['date'].min()} 到 {df['date'].max()}")
    
    if 'RIGSTA' in df.columns:
        rigsta_counts = df['RIGSTA'].value_counts()
        print(f"工况类型分布:")
        for rigsta, count in rigsta_counts.items():
            print(f"  {rigsta}: {count} 次")
    else:
        print("没有RIGSTA列")
        return
    
    # 创建测试输出目录
    test_output = "test_charts"
    os.makedirs(test_output, exist_ok=True)
    
    try:
        # 测试1: 生成单独的工况图
        print("\n🔄 测试1: 生成单独工况图...")
        rigsta_img = generate_rigsta_only_plot(
            csv_path=test_file,
            output_folder=test_output,
            figsize=(20, 6),
            dpi=150
        )
        if rigsta_img:
            print(f"✅ 工况图生成成功: {rigsta_img}")
        
        # 测试2: 生成包含工况的完整趋势图
        print("\n🔄 测试2: 生成完整趋势图（包含工况）...")
        trend_img = generate_trend_plot(
            csv_path=test_file,
            output_folder=test_output,
            figsize=(20, 25),
            dpi=150
        )
        if trend_img:
            print(f"✅ 完整趋势图生成成功: {trend_img}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print(f"\n📂 测试图表保存在: {os.path.abspath(test_output)}")

if __name__ == "__main__":
    test_rigsta_visualization()
