import pandas as pd
import os
import glob

# 获取脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 定义要提取的特征和对应的简称
feature_mapping = {
    '井深': 'DEP',      
    '钻头位置': 'BITDEP',     
    '大钩高度': 'HOKHEI',         
    '钻时': 'DRITIME',      
    '钻压': 'WOB',        
    '大钩负荷': 'HKLD',     
    '转盘转速': 'RPM',         
    '扭矩': 'TOR',  
    '入口流量': 'FLOWIN',
    '出口流量': 'FLOWOUT',
    '立压': 'SPP',         
    '套压': 'CSIP',
    '钻井状态': 'RIGSTA',   
}

# 英文特征列表 - 用于检测文件是否已转换
eng_features = list(feature_mapping.values())

# 创建输出目录
output_dir = os.path.join(script_dir, '已处理-工况')
os.makedirs(output_dir, exist_ok=True)

# 获取脚本所在目录下所有CSV文件
csv_files = glob.glob(os.path.join(script_dir, '*.csv'))

if not csv_files:
    print("脚本目录下没有找到CSV文件")
else:
    # 处理每个CSV文件
    for input_path in csv_files:
        try:
            print(f"正在处理: {input_path}")
            
            # 读取CSV文件
            df = pd.read_csv(input_path)
            
            # 检查文件是否已经转换
            already_converted = False
            
            # 检查是否已有date列
            if 'date' in df.columns:
                # 检查是否已有英文特征列
                eng_features_in_file = [col for col in eng_features if col in df.columns]
                if len(eng_features_in_file) >= len(eng_features) * 0.7:  # 如果70%以上的英文特征已存在，则视为已转换
                    already_converted = True
                    print(f"此文件已经转换过，将只保留所需列，并将date列移到最后一列")
            
            if already_converted:
                # 文件已转换，只保留所需列
                needed_columns = eng_features + ['date']  # 将date放在最后
                
                # 只选择文件中存在的eng_features列
                available_eng_columns = [col for col in eng_features if col in df.columns]
                
                # 确保有date列
                if 'date' in df.columns:
                    df_selected = df[available_eng_columns + ['date']]  # date列放在最后
                else:
                    df_selected = df[available_eng_columns]
                    df_selected['date'] = pd.Timestamp.now()  # 如果缺少date列，使用当前时间
                
                # 添加缺失的特征列并填充为NA
                missing_columns = [col for col in eng_features if col not in df.columns]
                for col in missing_columns:
                    df_selected[col] = pd.NA
                
                # 重新排列列，确保date在最后
                final_columns = [col for col in eng_features if col in df_selected.columns] + ['date']
                df_selected = df_selected[final_columns]
            else:
                # 文件未转换，需要进行转换
                # 检查是否有日期和时间列
                if '日期' in df.columns and '时间' in df.columns:
                    # 合并日期和时间列为date列
                    df['date'] = pd.to_datetime(df['日期'] + ' ' + df['时间'])
                elif '日期' in df.columns:  # 只有日期列
                    df['date'] = pd.to_datetime(df['日期'])
                elif 'date' not in df.columns:  # 既没有日期列也没有date列
                    # 使用文件名尝试提取日期时间信息，或使用当前时间作为替代
                    print(f"警告：文件 {input_path} 缺少日期列，将使用当前时间替代")
                    df['date'] = pd.Timestamp.now()
                
                # 创建新数据框用于存储结果
                df_selected = pd.DataFrame()
                
                # 处理特征列
                for ch_feature, eng_feature in feature_mapping.items():
                    if ch_feature in df.columns:
                        df_selected[eng_feature] = df[ch_feature]
                    elif eng_feature in df.columns:
                        df_selected[eng_feature] = df[eng_feature]
                    else:
                        print(f"警告：特征 {ch_feature}/{eng_feature} 在文件中不存在，将填充NA")
                        df_selected[eng_feature] = pd.NA
                
                # 将date列放在最后
                df_selected['date'] = df['date'] if 'date' in df.columns else pd.Timestamp.now()
            
            # 保持原文件名，但放在输出目录中
            filename = os.path.basename(input_path)
            output_path = os.path.join(output_dir, filename)
            
            # 确保dataframe包含所有必需的列，并按照要求排序
            final_columns = eng_features + ['date']  # date列在最后
            
            # 添加缺失列
            for col in final_columns:
                if col not in df_selected.columns:
                    df_selected[col] = pd.NA
            
            # 重新排列列顺序
            df_selected = df_selected[final_columns]
            
            # 保存处理后的数据
            df_selected.to_csv(output_path, index=False)
            
            print(f"✅ 完成: {output_path}")
            
        except Exception as e:
            print(f"❌ 处理文件 {input_path} 时出错: {str(e)}")
    
    print(f"\n共处理了 {len(csv_files)} 个CSV文件，结果保存在 {output_dir} 目录")