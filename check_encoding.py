import pandas as pd

# 检测文件编码（简单方法）
print("=== 文件编码检测 ===")
encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']

for encoding in encodings_to_try:
    try:
        with open('充浅1实时数据.csv', 'r', encoding=encoding) as f:
            f.read(100)  # 尝试读取前100个字符
        print(f'原始文件可能的编码: {encoding}')
        break
    except UnicodeDecodeError:
        continue

for encoding in encodings_to_try:
    try:
        with open('已处理-工况/充浅1实时数据.csv', 'r', encoding=encoding) as f:
            f.read(100)
        print(f'处理后文件可能的编码: {encoding}')
        break
    except UnicodeDecodeError:
        continue

# 读取并检查RIGSTA字段的具体内容
print("\n=== 原始文件中钻井状态字段的内容 ===")
df_orig = pd.read_csv('充浅1实时数据.csv')
print('钻井状态字段唯一值:')
unique_vals = df_orig['钻井状态'].unique()
for i, val in enumerate(unique_vals[:10]):
    print(f"{i+1}: {val}")

print("\n=== 处理后文件中RIGSTA字段的内容 ===")
df_processed = pd.read_csv('已处理-工况/充浅1实时数据.csv')
print('RIGSTA字段唯一值:')
unique_vals_processed = df_processed['RIGSTA'].unique()
for i, val in enumerate(unique_vals_processed[:10]):
    print(f"{i+1}: {val}")

# 检查字符编码
print("\n=== 字符编码详细检查 ===")
for i, val in enumerate(unique_vals_processed[:5]):
    if pd.notna(val):
        print(f"值 {i+1}: {repr(val)}")
        print(f"  类型: {type(val)}")
        try:
            encoded = val.encode('utf-8')
            print(f"  UTF-8编码: {encoded}")
            print(f"  字节长度: {len(encoded)}")
        except Exception as e:
            print(f"  编码失败: {e}")
        print()

# 检查是否有特殊字符
print("=== 特殊字符检查 ===")
for val in unique_vals_processed[:5]:
    if pd.notna(val):
        print(f"值: {val}")
        for char in val:
            print(f"  字符: '{char}' (Unicode: U+{ord(char):04X})")
        print()
