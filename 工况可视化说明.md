# 钻井工况可视化功能说明

## 功能概述

已为 `visual.py` 添加了钻井工况（RIGSTA字段）的可视化功能，可以将文本形式的工况数据转换为直观的图表。

## 工况类型映射

系统支持以下17种工况类型，每种工况都有对应的数值和颜色：

| 工况名称 | 数值 | 颜色 | 说明 |
|---------|------|------|------|
| 下钻 | 1 | 红色 | 钻具下入井内 |
| 停钻 | 2 | 青色 | 暂停钻进 |
| 划眼 | 3 | 蓝色 | 扩大井眼 |
| 坐卡 | 4 | 绿色 | 钻具卡住 |
| 循环 | 5 | 黄色 | 循环钻井液 |
| 接单根 | 6 | 紫色 | 连接钻杆 |
| 接立柱 | 7 | 薄荷绿 | 连接立管 |
| 接触井底 | 8 | 金黄色 | 钻头接触井底 |
| 接触井底/循环 | 9 | 淡紫色 | 接触井底并循环 |
| 提离井底 | 10 | 天蓝色 | 钻头离开井底 |
| 提离井底/循环 | 11 | 橙色 | 离开井底并循环 |
| 未定义 | 12 | 灰色 | 未定义状态 |
| 未知 | 13 | 浅灰色 | 未知状态 |
| 等待 | 14 | 粉红色 | 等待状态 |
| 起下钻 | 15 | 浅绿色 | 起下钻具 |
| 起钻 | 16 | 浅蓝色 | 起出钻具 |
| 钻进 | 17 | 浅黄色 | 正常钻进 |

## 新增功能

### 1. 工况数值转换
- `convert_rigsta_to_numeric()`: 将工况文本转换为数值
- 支持未知工况的处理（映射为0）

### 2. 工况时间轴绘制
- `plot_rigsta_timeline()`: 绘制工况随时间变化的散点图
- 不同工况用不同颜色区分
- 自动生成Y轴标签和图例

### 3. 单独工况图表
- `generate_rigsta_only_plot()`: 专门生成工况变化图表
- 适合专注分析工况变化模式

### 4. 集成到综合图表
- 在原有的参数趋势图中增加工况子图
- 保持时间轴同步，便于对比分析

## 使用方法

### 方法1: 运行visual.py（交互式）
```bash
cd 已处理-工况
python visual.py
```

会提示选择处理模式：
1. 生成完整趋势图（包含工况）
2. 只生成工况变化图  
3. 生成完整趋势图 + 单独工况图

### 方法2: 编程调用

```python
from visual import generate_rigsta_only_plot, generate_trend_plot

# 生成单独的工况图
generate_rigsta_only_plot(
    csv_path="your_file.csv",
    output_folder="charts",
    figsize=(22, 8),
    dpi=200
)

# 生成包含工况的完整趋势图
generate_trend_plot(
    csv_path="your_file.csv", 
    output_folder="charts",
    figsize=(22, 30),  # 增加高度以容纳工况图
    dpi=200
)
```

### 方法3: 批量处理

```python
from visual import batch_process_csv_files

# 只生成工况图
batch_process_csv_files(
    input_folder=".",
    output_folder="charts", 
    rigsta_only=True
)

# 生成完整图表 + 单独工况图
batch_process_csv_files(
    input_folder=".",
    output_folder="charts",
    include_rigsta=True,
    rigsta_only=False
)
```

## 图表特点

### 工况时间轴图
- X轴：时间
- Y轴：工况类型（数值化）
- 点的颜色：对应不同工况
- 点的密度：反映工况持续时间和频率

### 可视化优势
1. **直观性**：一眼看出工况变化模式
2. **时序性**：清楚显示工况随时间的演变
3. **对比性**：可与其他参数图表对比分析
4. **统计性**：通过点的分布了解各工况的占比

## 注意事项

1. **数据要求**：CSV文件必须包含 `RIGSTA` 和 `date` 列
2. **编码支持**：自动处理UTF-8-sig编码，确保中文正确显示
3. **内存使用**：大数据文件建议适当调整图表尺寸和DPI
4. **图例显示**：工况类型超过8种时自动隐藏图例以避免拥挤

## 扩展建议

1. **工况统计**：可添加工况时长统计功能
2. **异常检测**：识别异常的工况转换模式
3. **效率分析**：分析不同工况的钻进效率
4. **预测模型**：基于工况变化预测钻井进度
