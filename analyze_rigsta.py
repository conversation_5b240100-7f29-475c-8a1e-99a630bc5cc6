import pandas as pd
import glob
from collections import Counter

# 获取所有处理后的文件
processed_files = glob.glob('已处理-工况/*.csv')

print("=== 分析所有文件中的RIGSTA工况类型 ===")

all_rigsta_values = []
file_rigsta_info = {}

for file_path in processed_files:
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        if 'RIGSTA' in df.columns:
            # 获取该文件的工况类型
            unique_vals = df['RIGSTA'].dropna().unique()
            file_rigsta_info[file_path] = unique_vals
            
            # 收集所有工况值
            all_rigsta_values.extend(df['RIGSTA'].dropna().tolist())
            
            print(f"\n文件: {file_path.split('/')[-1]}")
            print(f"  工况类型数量: {len(unique_vals)}")
            for i, val in enumerate(unique_vals):
                print(f"    {i+1}: {val}")
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")

# 统计所有工况类型的频次
print("\n=== 全局工况类型统计 ===")
rigsta_counter = Counter(all_rigsta_values)
unique_rigsta_types = list(rigsta_counter.keys())

print(f"总共发现 {len(unique_rigsta_types)} 种不同的工况类型:")
for i, (rigsta_type, count) in enumerate(rigsta_counter.most_common()):
    print(f"{i+1:2d}. {rigsta_type:<15} (出现 {count:,} 次)")

# 为工况类型创建数值映射
print("\n=== 建议的工况数值映射 ===")
rigsta_mapping = {}
for i, rigsta_type in enumerate(sorted(unique_rigsta_types)):
    rigsta_mapping[rigsta_type] = i + 1
    print(f"{rigsta_type:<15} -> {i+1}")

print(f"\n映射字典:")
print("RIGSTA_MAPPING = {")
for rigsta_type, value in rigsta_mapping.items():
    print(f"    '{rigsta_type}': {value},")
print("}")

print(f"\n分析完成！共分析了 {len(processed_files)} 个文件。")
