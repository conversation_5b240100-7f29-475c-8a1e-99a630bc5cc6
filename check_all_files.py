import pandas as pd
import os
import glob

# 获取所有处理后的文件
processed_files = glob.glob('已处理-工况/*.csv')

print("=== 检查所有处理后文件的RIGSTA字段 ===")
for file_path in processed_files[:5]:  # 检查前5个文件
    print(f"\n文件: {os.path.basename(file_path)}")
    try:
        df = pd.read_csv(file_path)
        if 'RIGSTA' in df.columns:
            unique_vals = df['RIGSTA'].unique()
            print(f"RIGSTA唯一值数量: {len(unique_vals)}")
            for i, val in enumerate(unique_vals[:5]):
                if pd.notna(val):
                    print(f"  {i+1}: {repr(val)}")
                    # 检查是否包含非ASCII字符
                    try:
                        val.encode('ascii')
                        print(f"     -> ASCII兼容")
                    except UnicodeEncodeError:
                        print(f"     -> 包含非ASCII字符（中文）")
        else:
            print("  没有RIGSTA字段")
    except Exception as e:
        print(f"  读取文件出错: {e}")

# 检查是否有真正的乱码文件
print("\n=== 检查是否存在乱码问题 ===")
for file_path in processed_files:
    try:
        df = pd.read_csv(file_path)
        if 'RIGSTA' in df.columns:
            for val in df['RIGSTA'].dropna().unique():
                # 检查是否包含乱码字符（如问号、方块等）
                if '?' in str(val) or '□' in str(val) or '�' in str(val):
                    print(f"发现乱码文件: {file_path}")
                    print(f"乱码值: {repr(val)}")
                    break
    except Exception as e:
        print(f"检查文件 {file_path} 时出错: {e}")

print("\n检查完成！")
